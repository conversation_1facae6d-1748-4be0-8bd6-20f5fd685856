{"name": "moving-transport-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:prod": "NODE_ENV=production tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "echo \"No tests specified\" && exit 0", "type-check": "tsc -b --noEmit", "clean": "rm -rf dist", "deploy:build": "npm run clean && npm run type-check && npm run lint && npm run build:prod"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^4.2.1", "react-router-dom": "^7.6.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@types/leaflet": "^1.9.8", "@types/node": "^24.0.12", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}