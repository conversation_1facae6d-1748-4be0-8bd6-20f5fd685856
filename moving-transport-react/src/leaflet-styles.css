/* Import Leaflet CSS */
@import url('https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');

/* Leaflet map container */
.leaflet-map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

/* Make sure the map takes full height */
.leaflet-container {
  width: 100%;
  height: 100%;
}

/* Custom marker styles */
.custom-marker {
  background: none;
  border: none;
}

/* Pulse animation for selected markers */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(25, 118, 210, 0); }
  100% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0); }
}

/* Olive theme pulse animation */
@keyframes pulse-olive {
  0% { box-shadow: 0 0 0 0 rgba(107, 124, 50, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(107, 124, 50, 0); }
  100% { box-shadow: 0 0 0 0 rgba(107, 124, 50, 0); }
}

/* Vehicle movement animation */
@keyframes vehicle-drift {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(2px, -1px) rotate(1deg); }
  50% { transform: translate(1px, -2px) rotate(-1deg); }
  75% { transform: translate(-1px, -1px) rotate(0.5deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

/* Map tile styling for olive theme */
.leaflet-tile {
  filter: sepia(10%) saturate(90%) hue-rotate(60deg) brightness(95%);
}

/* Custom vehicle marker animations */
.animated-vehicle-marker {
  animation: vehicle-drift 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.animated-vehicle-marker:hover {
  animation-play-state: paused;
  transform: scale(1.1);
}

/* Popup styles */
.leaflet-popup-content-wrapper {
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
}

.leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.leaflet-popup-tip-container {
  margin-top: -1px;
}

/* Hide attribution control on small screens */
@media (max-width: 600px) {
  .leaflet-control-attribution {
    display: none;
  }
}
