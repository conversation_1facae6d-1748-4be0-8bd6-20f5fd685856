import type { AnimatedVehicle, ServiceMode } from '../types/models';
import { transportOptions } from './mockData';

// Generate random coordinates around a center point
const generateRandomCoordinates = (
  center: { lat: number; lng: number },
  radiusKm: number = 10
): { lat: number; lng: number } => {
  const radiusInDegrees = radiusKm / 111; // Rough conversion: 1 degree ≈ 111 km
  
  const angle = Math.random() * 2 * Math.PI;
  const radius = Math.random() * radiusInDegrees;
  
  return {
    lat: center.lat + radius * Math.cos(angle),
    lng: center.lng + radius * Math.sin(angle)
  };
};

// Generate a circular path for vehicle animation
const generateAnimationPath = (
  center: { lat: number; lng: number },
  radiusKm: number = 2,
  points: number = 8
): { lat: number; lng: number }[] => {
  const path: { lat: number; lng: number }[] = [];
  const radiusInDegrees = radiusKm / 111;
  
  for (let i = 0; i < points; i++) {
    const angle = (i / points) * 2 * Math.PI;
    path.push({
      lat: center.lat + radiusInDegrees * Math.cos(angle),
      lng: center.lng + radiusInDegrees * Math.sin(angle)
    });
  }
  
  return path;
};

// Service-specific vehicle configurations
const serviceVehicleConfigs = {
  'hitch-freight': {
    types: ['Small Van', 'Large Van', 'Small Truck', 'Medium Truck'],
    speedRange: [3, 8], // seconds between moves
    count: 12
  },
  'people-mover': {
    types: ['Large Van', 'Small Truck', 'Medium Truck', 'Large Truck'],
    speedRange: [4, 10],
    count: 8
  },
  'car-me': {
    types: ['Small Van', 'Pickup Truck'], // Representing cars as smaller vehicles
    speedRange: [2, 6],
    count: 15
  }
};

// Generate animated vehicles based on service mode
export const generateAnimatedVehicles = (
  userLocation: { lat: number; lng: number },
  serviceMode: ServiceMode,
  filters?: any
): AnimatedVehicle[] => {
  const config = serviceVehicleConfigs[serviceMode];
  const vehicles: AnimatedVehicle[] = [];
  
  // Filter base transport options by service-appropriate types
  const serviceTransportOptions = transportOptions.filter(option => 
    config.types.includes(option.type)
  );
  
  for (let i = 0; i < config.count; i++) {
    // Select a random base transport option
    const baseOption = serviceTransportOptions[i % serviceTransportOptions.length];
    
    // Generate random starting position around user location
    const startPosition = generateRandomCoordinates(userLocation, 15);
    
    // Generate animation path
    const animationPath = generateAnimationPath(startPosition, Math.random() * 3 + 1);
    
    // Calculate random speed
    const speed = Math.random() * (config.speedRange[1] - config.speedRange[0]) + config.speedRange[0];
    
    // Generate random direction
    const direction = Math.random() * 360;
    
    // Create animated vehicle
    const animatedVehicle: AnimatedVehicle = {
      ...baseOption,
      id: baseOption.id + i * 1000, // Ensure unique IDs
      currentPosition: startPosition,
      animationPath,
      speed,
      direction,
      serviceMode,
      // Randomize some properties for variety
      pricePerHour: Math.floor(Math.random() * 100) + 30,
      price: `$${Math.floor(Math.random() * 100) + 30}/hour`,
      distance: Math.round((Math.random() * 20 + 1) * 10) / 10,
      rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
      coordinates: startPosition
    };
    
    vehicles.push(animatedVehicle);
  }
  
  // Apply filters if provided
  if (filters) {
    return vehicles.filter(vehicle => {
      // Price filter
      if (vehicle.pricePerHour < filters.priceRange[0] || vehicle.pricePerHour > filters.priceRange[1]) {
        return false;
      }
      
      // Distance filter
      if (vehicle.distance > filters.distance) {
        return false;
      }
      
      // Rating filter
      if (vehicle.rating < filters.rating) {
        return false;
      }
      
      // Vehicle type filter
      if (filters.vehicleTypes.length > 0 && !filters.vehicleTypes.includes(vehicle.type.toLowerCase().replace(' ', ''))) {
        return false;
      }
      
      // Driver included filter
      if (filters.driverIncluded && !vehicle.driverIncluded) {
        return false;
      }
      
      return true;
    });
  }
  
  return vehicles;
};

// Generate service-specific mock data for different scenarios
export const generateServiceMockData = (serviceMode: ServiceMode) => {
  const baseData = {
    'hitch-freight': {
      title: 'Freight Transport Available',
      description: 'Professional cargo and freight transportation services',
      features: ['Cargo Loading', 'Secure Transport', 'Tracking Available', 'Insurance Included'],
      pricing: 'Starting from $45/hour'
    },
    'people-mover': {
      title: 'Moving Services Available',
      description: 'Complete moving and relocation services with professional helpers',
      features: ['Professional Movers', 'Packing Materials', 'Furniture Assembly', 'Full Insurance'],
      pricing: 'Starting from $65/hour'
    },
    'car-me': {
      title: 'Car Sharing Available',
      description: 'Peer-to-peer car sharing and rental services',
      features: ['Instant Booking', 'Fuel Included', '24/7 Support', 'Comprehensive Insurance'],
      pricing: 'Starting from $25/hour'
    }
  };
  
  return baseData[serviceMode];
};

// Generate realistic movement patterns for different vehicle types
export const generateRealisticMovementPattern = (
  _vehicleType: string,
  serviceMode: ServiceMode,
  userLocation: { lat: number; lng: number }
): { lat: number; lng: number }[] => {
  const patterns = {
    'hitch-freight': {
      // Freight vehicles tend to follow main roads and highways
      pattern: 'highway',
      speed: 'medium',
      radius: 20
    },
    'people-mover': {
      // Moving trucks follow residential and commercial routes
      pattern: 'residential',
      speed: 'slow',
      radius: 15
    },
    'car-me': {
      // Cars have more flexible movement patterns
      pattern: 'flexible',
      speed: 'fast',
      radius: 25
    }
  };
  
  const config = patterns[serviceMode];
  const path: { lat: number; lng: number }[] = [];
  
  // Generate path based on pattern type
  switch (config.pattern) {
    case 'highway':
      // Linear movement along main routes
      for (let i = 0; i < 6; i++) {
        path.push({
          lat: userLocation.lat + (i - 3) * 0.01,
          lng: userLocation.lng + (i - 3) * 0.015
        });
      }
      break;
      
    case 'residential':
      // Grid-like movement through neighborhoods
      for (let i = 0; i < 8; i++) {
        const gridX = (i % 3) - 1;
        const gridY = Math.floor(i / 3) - 1;
        path.push({
          lat: userLocation.lat + gridY * 0.008,
          lng: userLocation.lng + gridX * 0.012
        });
      }
      break;
      
    case 'flexible':
    default:
      // Circular movement with variations
      return generateAnimationPath(userLocation, config.radius / 10);
  }
  
  return path;
};
