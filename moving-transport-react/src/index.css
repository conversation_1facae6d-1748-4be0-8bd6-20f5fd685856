@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, rgba(107, 124, 50, 0.03) 0%, rgba(212, 175, 55, 0.05) 25%, rgba(74, 86, 34, 0.08) 50%, rgba(212, 175, 55, 0.05) 75%, rgba(107, 124, 50, 0.03) 100%);
  color: #2C2C2C;
  min-height: 100vh;
  /* Enable backdrop-filter support */
  -webkit-backdrop-filter: blur(0);
  backdrop-filter: blur(0);
}

html {
  scroll-behavior: smooth;
}

/* Neo-brutalism base styles */
* {
  border-radius: 0 !important;
}

/* Animation keyframes for the new Land page */
@keyframes pulse-user {
  0% { box-shadow: 0 0 0 0 rgba(107, 124, 50, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(107, 124, 50, 0); }
  100% { box-shadow: 0 0 0 0 rgba(107, 124, 50, 0); }
}

@keyframes vehicle-move {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(5px) translateY(-10px); }
  75% { transform: translateX(-5px) translateY(-5px); }
  100% { transform: translateX(0) translateY(0); }
}

@keyframes search-container-slide {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100px);
    opacity: 0.95;
  }
}

@keyframes map-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom animations for vehicle markers */
.animated-vehicle-marker {
  animation: vehicle-move 4s ease-in-out infinite;
}

.user-location-marker {
  animation: pulse-user 2s infinite;
}

/* Geometric button styles with liquid glass effects */
@layer components {
  .btn-geometric {
    @apply border-2 border-olive-primary bg-olive-primary text-white font-bold px-6 py-3 transition-all duration-200 hover:bg-olive-dark hover:border-olive-dark;
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
  }

  .btn-geometric-secondary {
    @apply border-2 border-gold-accent bg-transparent text-gold-accent font-bold px-6 py-3 transition-all duration-200 hover:bg-gold-accent hover:text-white;
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
  }

  .card-geometric {
    @apply border-2 border-olive-primary bg-white shadow-geometric transition-all duration-200 hover:shadow-geometric-hover;
    backdrop-filter: blur(8px) saturate(120%);
    -webkit-backdrop-filter: blur(8px) saturate(120%);
    background: rgba(255, 255, 255, 0.9);
  }

  .card-geometric-glass {
    @apply border-2 border-olive-primary shadow-geometric transition-all duration-200 hover:shadow-geometric-hover;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(107, 124, 50, 0.3);
  }

  .liquid-glass-container {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: linear-gradient(135deg, rgba(107, 124, 50, 0.05) 0%, rgba(212, 175, 55, 0.08) 25%, rgba(74, 86, 34, 0.12) 50%, rgba(212, 175, 55, 0.08) 75%, rgba(107, 124, 50, 0.05) 100%);
    position: relative;
  }

  .liquid-glass-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
  }
}

/* Map styles */
.leaflet-map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
}
