// Transport option interface
export interface TransportOption {
  id: number;
  type: string;
  title: string;
  price: string;
  pricePerHour: number;
  image: string;
  features: {
    capacity: string;
    volume: string;
    driver: string;
  };
  rating: number;
  reviews: number;
  location: string;
  distance: number;
  coordinates: {
    lng: number;
    lat: number;
  };
  // Additional properties for liquid glass design
  verified?: boolean;
  capacity?: string;
  size?: string;
  driverIncluded?: boolean;
}

// Testimonial interface
export interface Testimonial {
  id: number;
  name: string;
  location: string;
  avatar: string;
  rating: number;
  text: string;
}

// Vehicle types as string union type
export type VehicleTypes = 'van' | 'truck' | 'pickup' | 'smallVan' | 'largeVan' | 'smallTruck' | 'largeTruck' | 'pickupTruck' | 'boxTruck';

// Service modes for the new search functionality
export type ServiceMode = 'hitch-freight' | 'people-mover' | 'car-me';

// Service configuration interface
export interface ServiceConfig {
  id: ServiceMode;
  name: string;
  description: string;
  icon: string;
  color: string;
  vehicleTypes: VehicleTypes[];
}

// Individual search form interfaces for each service mode
export interface HitchFreightFormData {
  destination: string;
  cargoType: string;
  pickupLocation: string;
  datetime: string;
}

export interface PeopleMoverFormData {
  origin: string;
  destination: string;
  datetime: string;
  helpers: boolean;
}

export interface CarMeFormData {
  location: string;
  pickupDate: string;
  dropoffDate: string;
  carType: string;
}

// Combined search form data interface
export interface SearchFormData {
  'hitch-freight': HitchFreightFormData;
  'people-mover': PeopleMoverFormData;
  'car-me': CarMeFormData;
}

// Type helper for getting form data by service mode
export type ServiceFormData<T extends ServiceMode> = SearchFormData[T];

// Animated vehicle marker interface
export interface AnimatedVehicle extends TransportOption {
  animationPath: { lat: number; lng: number }[];
  currentPosition: { lat: number; lng: number };
  speed: number;
  direction: number;
  serviceMode: ServiceMode;
}

// Vehicle filter state interface for UI components
export interface VehicleFilterState {
  smallVan: boolean;
  largeVan: boolean;
  smallTruck: boolean;
  largeTruck: boolean;
  pickupTruck: boolean;
  boxTruck: boolean;
}

// Filter options interface
export interface FilterOptions {
  priceRange: number[];
  distance: number;
  vehicleTypes: VehicleTypes[];
  sortBy: string;
  rating: number;
  driverIncluded: boolean;
  serviceMode?: ServiceMode;
}
