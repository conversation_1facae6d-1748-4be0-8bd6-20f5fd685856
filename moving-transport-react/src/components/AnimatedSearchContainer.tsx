import { useState } from 'react';
import { Search, Package, Users, Car, MapPin, Calendar, Clock } from 'lucide-react';
import type { ServiceMode, ServiceFormData } from '../types/models';

interface AnimatedSearchContainerProps {
  activeService: ServiceMode;
  onServiceChange: (service: ServiceMode) => void;
  onSearch: (formData: ServiceFormData<ServiceMode>) => void;
  searchPerformed: boolean;
  formData: ServiceFormData<ServiceMode>;
}

const AnimatedSearchContainer = ({
  activeService,
  onServiceChange,
  onSearch,
  searchPerformed,
  formData
}: AnimatedSearchContainerProps) => {
  const [localFormData, setLocalFormData] = useState(formData);

  const services = [
    {
      id: 'hitch-freight' as ServiceMode,
      name: 'Hitch Freight',
      description: 'Freight hitching service for cargo transport',
      icon: Package,
      color: 'olive-primary'
    },
    {
      id: 'people-mover' as ServiceMode,
      name: 'People Mover',
      description: 'Passenger transportation service',
      icon: Users,
      color: 'gold-accent'
    },
    {
      id: 'car-me' as ServiceMode,
      name: 'Car Me',
      description: 'Peer-to-peer car sharing/borrowing service',
      icon: Car,
      color: 'amber-accent'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localFormData);
  };

  const renderHitchFreightForm = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <MapPin className="inline w-4 h-4 mr-2" />
          CARGO DESTINATION
        </label>
        <input
          type="text"
          placeholder="Where is your cargo going?"
          value={(localFormData as any).destination || ''}
          onChange={(e) => handleInputChange('destination', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text placeholder-secondary-text liquid-glass-subtle"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <Package className="inline w-4 h-4 mr-2" />
          CARGO TYPE
        </label>
        <select
          value={(localFormData as any).cargoType || ''}
          onChange={(e) => handleInputChange('cargoType', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
          required
        >
          <option value="">Select cargo type</option>
          <option value="furniture">Furniture</option>
          <option value="appliances">Appliances</option>
          <option value="boxes">Boxes/Packages</option>
          <option value="equipment">Equipment</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <MapPin className="inline w-4 h-4 mr-2" />
          PICKUP LOCATION
        </label>
        <input
          type="text"
          placeholder="Where should we pick up your cargo?"
          value={(localFormData as any).pickupLocation || ''}
          onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text placeholder-secondary-text liquid-glass-subtle"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <Clock className="inline w-4 h-4 mr-2" />
          WHEN
        </label>
        <input
          type="datetime-local"
          value={(localFormData as any).datetime || ''}
          onChange={(e) => handleInputChange('datetime', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
          required
        />
      </div>
    </div>
  );

  const renderPeopleMoverForm = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <MapPin className="inline w-4 h-4 mr-2" />
          ORIGIN (CURRENT LOCATION)
        </label>
        <input
          type="text"
          placeholder="Where are you moving from?"
          value={(localFormData as any).origin || ''}
          onChange={(e) => handleInputChange('origin', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text placeholder-secondary-text liquid-glass-subtle"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <MapPin className="inline w-4 h-4 mr-2" />
          DESTINATION
        </label>
        <input
          type="text"
          placeholder="Where are you moving to?"
          value={(localFormData as any).destination || ''}
          onChange={(e) => handleInputChange('destination', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text placeholder-secondary-text liquid-glass-subtle"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <Clock className="inline w-4 h-4 mr-2" />
          WHEN
        </label>
        <input
          type="datetime-local"
          value={(localFormData as any).datetime || ''}
          onChange={(e) => handleInputChange('datetime', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
          required
        />
      </div>

      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="helpers"
          checked={(localFormData as any).helpers || false}
          onChange={(e) => handleInputChange('helpers', e.target.checked)}
          className="w-5 h-5 border-2 border-olive-primary focus:border-gold-accent"
        />
        <label htmlFor="helpers" className="text-sm font-bold text-olive-dark">
          I need moving helpers
        </label>
      </div>
    </div>
  );

  const renderCarMeForm = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <MapPin className="inline w-4 h-4 mr-2" />
          LOCATION
        </label>
        <input
          type="text"
          placeholder="Where do you need a car?"
          value={(localFormData as any).location || ''}
          onChange={(e) => handleInputChange('location', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text placeholder-secondary-text liquid-glass-subtle"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-bold text-olive-dark mb-2">
            <Calendar className="inline w-4 h-4 mr-2" />
            PICKUP DATE & TIME
          </label>
          <input
            type="datetime-local"
            value={(localFormData as any).pickupDate || ''}
            onChange={(e) => handleInputChange('pickupDate', e.target.value)}
            className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-bold text-olive-dark mb-2">
            <Calendar className="inline w-4 h-4 mr-2" />
            DROPOFF DATE & TIME
          </label>
          <input
            type="datetime-local"
            value={(localFormData as any).dropoffDate || ''}
            onChange={(e) => handleInputChange('dropoffDate', e.target.value)}
            className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-bold text-olive-dark mb-2">
          <Car className="inline w-4 h-4 mr-2" />
          CAR TYPE
        </label>
        <select
          value={(localFormData as any).carType || ''}
          onChange={(e) => handleInputChange('carType', e.target.value)}
          className="w-full px-6 py-4 border-4 border-olive-primary focus:border-gold-accent focus:outline-none font-bold text-dark-text liquid-glass-subtle"
          required
        >
          <option value="">Select car type</option>
          <option value="economy">Economy</option>
          <option value="compact">Compact</option>
          <option value="midsize">Midsize</option>
          <option value="fullsize">Full Size</option>
          <option value="luxury">Luxury</option>
          <option value="suv">SUV</option>
          <option value="truck">Pickup Truck</option>
        </select>
      </div>
    </div>
  );

  const renderActiveForm = () => {
    switch (activeService) {
      case 'hitch-freight':
        return renderHitchFreightForm();
      case 'people-mover':
        return renderPeopleMoverForm();
      case 'car-me':
        return renderCarMeForm();
      default:
        return null;
    }
  };

  return (
    <div className={`
      transition-all duration-700 ease-in-out
      ${searchPerformed 
        ? 'fixed top-20 left-0 right-0 z-40 bg-liquid-glass-olive border-b-4 border-olive-primary' 
        : 'flex items-center justify-center min-h-screen p-8'
      }
    `}>
      <div className={`
        w-full max-w-2xl mx-auto
        ${searchPerformed ? 'py-4' : ''}
      `}>
        {/* Service Mode Selector */}
        <div className="mb-8">
          <h1 className="text-4xl font-black text-olive-dark mb-4 text-center">
            FIND YOUR PERFECT MOVE
          </h1>
          <p className="text-olive-dark/80 text-center mb-8 font-medium">
            Choose your service and let us help you get there
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {services.map((service) => {
              const Icon = service.icon;
              return (
                <button
                  key={service.id}
                  onClick={() => onServiceChange(service.id)}
                  className={`
                    p-6 border-4 transition-all duration-200 liquid-glass-container
                    ${activeService === service.id
                      ? 'border-gold-accent bg-liquid-glass-gold shadow-geometric'
                      : 'border-olive-primary hover:border-gold-accent hover:bg-liquid-glass-subtle'
                    }
                  `}
                >
                  <Icon className={`w-8 h-8 mx-auto mb-3 ${
                    activeService === service.id ? 'text-gold-dark' : 'text-olive-primary'
                  }`} />
                  <h3 className="font-black text-lg text-olive-dark mb-2">
                    {service.name}
                  </h3>
                  <p className="text-sm text-olive-dark/70 font-medium">
                    {service.description}
                  </p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Search Form */}
        <form onSubmit={handleSubmit} className="liquid-glass-container border-4 border-olive-primary p-8">
          <div className="mb-6">
            <h2 className="text-2xl font-black text-olive-dark mb-2">
              {services.find(s => s.id === activeService)?.name} SEARCH
            </h2>
            <p className="text-olive-dark/70 font-medium">
              {services.find(s => s.id === activeService)?.description}
            </p>
          </div>

          {renderActiveForm()}

          <button
            type="submit"
            className="w-full mt-8 px-8 py-4 bg-olive-primary border-4 border-olive-primary text-white font-black text-lg hover:bg-olive-dark hover:border-olive-dark transition-all duration-200 shadow-geometric hover:shadow-geometric-hover flex items-center justify-center gap-3"
          >
            <Search className="w-6 h-6" />
            SEARCH {services.find(s => s.id === activeService)?.name.toUpperCase()}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AnimatedSearchContainer;
