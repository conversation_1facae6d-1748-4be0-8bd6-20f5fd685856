import { useRef, useEffect, useState } from 'react';
import type { ServiceMode, AnimatedVehicle } from '../types/models';
import '../leaflet-styles.css';

interface InteractiveMapProps {
  userLocation: { lat: number; lng: number } | null;
  animatedVehicles: AnimatedVehicle[];
  activeService: ServiceMode;
  onVehicleSelect: (vehicle: AnimatedVehicle) => void;
}

const InteractiveMap = ({
  userLocation,
  animatedVehicles,
  activeService,
  onVehicleSelect
}: InteractiveMapProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<{[key: number]: any}>({});
  const [mapInitialized, setMapInitialized] = useState(false);
  const animationFrameRef = useRef<number | null>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || typeof window === 'undefined') return;

    const loadLeaflet = async () => {
      try {
        // Dynamically import Leaflet
        const L = await import('leaflet');
        
        // Fix for default markers
        delete (L.Icon.Default.prototype as any)._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        if (mapInstanceRef.current || !mapRef.current) return;

        const defaultLocation = userLocation || { lat: 40.7128, lng: -74.0060 };

        // Initialize map
        const map = L.map(mapRef.current).setView([defaultLocation.lat, defaultLocation.lng], 12);

        // Add custom tile layer with olive theme
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          className: 'map-tiles'
        }).addTo(map);

        // Add user location marker
        if (userLocation) {
          const userIcon = L.divIcon({
            className: 'user-location-marker',
            html: `<div style="
              width: 20px;
              height: 20px;
              background-color: #6B7C32;
              border: 4px solid #D4AF37;
              border-radius: 50%;
              box-shadow: 0 0 0 4px rgba(107, 124, 50, 0.3);
              animation: pulse-user 2s infinite;
            "></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
          });

          L.marker([userLocation.lat, userLocation.lng], { icon: userIcon })
            .addTo(map)
            .bindPopup('<div class="font-bold text-olive-dark">Your Location</div>');
        }

        mapInstanceRef.current = map;
        setMapInitialized(true);
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    loadLeaflet();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [userLocation]);

  // Update vehicle markers when vehicles change
  useEffect(() => {
    if (!mapInitialized || !mapInstanceRef.current) return;

    const updateMarkers = async () => {
      const L = await import('leaflet');

    // Clear existing markers
    Object.values(markersRef.current).forEach((marker: any) => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = {};

    // Add new vehicle markers
    animatedVehicles.forEach((vehicle) => {
      const serviceColors = {
        'hitch-freight': '#6B7C32', // olive-primary
        'people-mover': '#D4AF37',  // gold-accent
        'car-me': '#FFC107'         // amber-accent
      };

      const color = serviceColors[vehicle.serviceMode];
      
      const vehicleIcon = L.divIcon({
        className: 'animated-vehicle-marker',
        html: `<div style="
          width: 36px;
          height: 36px;
          background-color: ${color};
          border: 3px solid white;
          border-radius: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          color: white;
          font-weight: bold;
          font-size: 12px;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
          transform: rotate(${vehicle.direction}deg);
          transition: all 0.3s ease;
          cursor: pointer;
        ">$${vehicle.pricePerHour}</div>`,
        iconSize: [36, 36],
        iconAnchor: [18, 18]
      });

      const marker = L.marker([vehicle.currentPosition.lat, vehicle.currentPosition.lng], {
        icon: vehicleIcon
      }).addTo(mapInstanceRef.current);

      marker.on('click', () => {
        onVehicleSelect(vehicle);
      });

      // Add popup with vehicle details
      marker.bindPopup(`
        <div class="liquid-glass-container p-4 border-2 border-olive-primary">
          <h3 class="font-black text-olive-dark mb-2">${vehicle.title}</h3>
          <p class="text-sm text-olive-dark/70 mb-2">${vehicle.type}</p>
          <div class="flex justify-between items-center">
            <span class="font-bold text-gold-accent">${vehicle.price}</span>
            <span class="text-xs text-olive-dark">${vehicle.distance} miles away</span>
          </div>
          <div class="mt-2 text-xs text-olive-dark/60">
            Service: ${vehicle.serviceMode.replace('-', ' ').toUpperCase()}
          </div>
        </div>
      `);

      markersRef.current[vehicle.id] = marker;
    });
    };

    updateMarkers();
  }, [animatedVehicles, mapInitialized, onVehicleSelect]);

  // Animate vehicle movement
  useEffect(() => {
    if (!mapInitialized || animatedVehicles.length === 0) return;

    const animateVehicles = () => {
      animatedVehicles.forEach((vehicle) => {
        const marker = markersRef.current[vehicle.id];
        if (!marker) return;

        // Simple animation: move vehicle along its path
        const pathIndex = Math.floor(Date.now() / (vehicle.speed * 1000)) % vehicle.animationPath.length;
        const nextPosition = vehicle.animationPath[pathIndex];
        
        if (nextPosition) {
          marker.setLatLng([nextPosition.lat, nextPosition.lng]);
          
          // Update vehicle's current position
          vehicle.currentPosition = nextPosition;
          
          // Calculate direction for next movement
          const nextIndex = (pathIndex + 1) % vehicle.animationPath.length;
          const nextPoint = vehicle.animationPath[nextIndex];
          if (nextPoint) {
            const angle = Math.atan2(
              nextPoint.lng - nextPosition.lng,
              nextPoint.lat - nextPosition.lat
            ) * (180 / Math.PI);
            vehicle.direction = angle;
          }
        }
      });

      animationFrameRef.current = requestAnimationFrame(animateVehicles);
    };

    animateVehicles();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [animatedVehicles, mapInitialized]);

  return (
    <div className="relative w-full h-full">
      {/* Map Container */}
      <div 
        ref={mapRef} 
        className="w-full h-full leaflet-map-container"
        style={{ minHeight: '600px' }}
      />
      
      {/* Service Mode Indicator */}
      <div className="absolute top-4 right-4 liquid-glass-container border-2 border-olive-primary p-3">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 ${
            activeService === 'hitch-freight' ? 'bg-olive-primary' :
            activeService === 'people-mover' ? 'bg-gold-accent' :
            'bg-amber-accent'
          }`}></div>
          <span className="text-xs font-bold text-olive-dark uppercase">
            {activeService.replace('-', ' ')} MODE
          </span>
        </div>
      </div>

      {/* Live Indicator */}
      <div className="absolute top-4 left-4 liquid-glass-gold border-2 border-gold-accent p-2">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-gold-accent animate-pulse"></div>
          <span className="text-xs font-bold text-olive-dark">LIVE MAP</span>
        </div>
      </div>

      {/* Vehicle Count */}
      <div className="absolute bottom-4 right-4 liquid-glass-container border-2 border-olive-primary p-3">
        <div className="text-center">
          <div className="text-lg font-black text-olive-dark">{animatedVehicles.length}</div>
          <div className="text-xs text-olive-dark/70">AVAILABLE</div>
        </div>
      </div>
    </div>
  );
};

export default InteractiveMap;
