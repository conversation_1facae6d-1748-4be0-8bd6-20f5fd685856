import { useEffect, useState } from 'react';
import type { AnimatedVehicle, ServiceMode } from '../types/models';

interface AnimatedVehicleMarkerProps {
  vehicle: AnimatedVehicle;
  isSelected: boolean;
  onClick: () => void;
}

const AnimatedVehicleMarker = ({ vehicle, isSelected, onClick }: AnimatedVehicleMarkerProps) => {
  const [currentPathIndex, setCurrentPathIndex] = useState(0);

  // Animate vehicle movement along path
  useEffect(() => {
    if (vehicle.animationPath.length === 0) return;

    const interval = setInterval(() => {
      setCurrentPathIndex((prevIndex) => 
        (prevIndex + 1) % vehicle.animationPath.length
      );
    }, vehicle.speed * 1000);

    return () => clearInterval(interval);
  }, [vehicle.animationPath.length, vehicle.speed]);

  // Update vehicle's current position
  useEffect(() => {
    if (vehicle.animationPath[currentPathIndex]) {
      vehicle.currentPosition = vehicle.animationPath[currentPathIndex];
    }
  }, [currentPathIndex, vehicle]);

  const getServiceColor = (serviceMode: ServiceMode) => {
    switch (serviceMode) {
      case 'hitch-freight':
        return '#6B7C32'; // olive-primary
      case 'people-mover':
        return '#D4AF37'; // gold-accent
      case 'car-me':
        return '#FFC107'; // amber-accent
      default:
        return '#6B7C32';
    }
  };

  const getServiceIcon = (serviceMode: ServiceMode) => {
    switch (serviceMode) {
      case 'hitch-freight':
        return '📦';
      case 'people-mover':
        return '🚛';
      case 'car-me':
        return '🚗';
      default:
        return '🚗';
    }
  };

  const color = getServiceColor(vehicle.serviceMode);
  const icon = getServiceIcon(vehicle.serviceMode);

  return (
    <div
      onClick={onClick}
      className={`
        absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer
        transition-all duration-300 ease-in-out
        ${isSelected ? 'scale-110 z-20' : 'z-10'}
      `}
      style={{
        left: `${((vehicle.currentPosition.lng + 180) / 360) * 100}%`,
        top: `${((90 - vehicle.currentPosition.lat) / 180) * 100}%`,
        transform: `translate(-50%, -50%) rotate(${vehicle.direction}deg)`,
      }}
    >
      {/* Vehicle Marker */}
      <div
        className={`
          w-9 h-9 flex items-center justify-center font-bold text-white text-xs
          border-3 border-white shadow-lg transition-all duration-300
          ${isSelected ? 'animate-pulse' : ''}
        `}
        style={{
          backgroundColor: color,
          boxShadow: isSelected 
            ? `0 0 0 4px ${color}40, 0 4px 8px rgba(0, 0, 0, 0.3)`
            : '0 4px 8px rgba(0, 0, 0, 0.3)'
        }}
      >
        ${vehicle.pricePerHour}
      </div>

      {/* Service Type Indicator */}
      <div
        className="absolute -top-2 -right-2 w-5 h-5 bg-white border-2 flex items-center justify-center text-xs"
        style={{ borderColor: color }}
      >
        {icon}
      </div>

      {/* Movement Trail Effect */}
      {isSelected && (
        <div
          className="absolute inset-0 animate-ping"
          style={{
            backgroundColor: color,
            opacity: 0.3,
            borderRadius: '50%',
          }}
        />
      )}

      {/* Tooltip on Hover */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-white border-2 border-olive-primary p-2 shadow-lg min-w-max">
          <div className="font-bold text-olive-dark text-sm">{vehicle.title}</div>
          <div className="text-xs text-olive-dark/70">{vehicle.type}</div>
          <div className="text-xs text-gold-accent font-bold">{vehicle.price}</div>
          <div className="text-xs text-olive-dark/60">
            {vehicle.distance} miles • {vehicle.rating}⭐
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnimatedVehicleMarker;
