import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import GeometricHeader from './GeometricHeader';
import AnimatedSearchContainer from './AnimatedSearchContainer';
import InteractiveMap from './InteractiveMap';
import FilterBar from './FilterBar';
import type { ServiceMode, SearchFormData, AnimatedVehicle, ServiceFormData } from '../types/models';
import { generateAnimatedVehicles } from '../data/animatedVehicleData';

const LandPage = () => {
  const navigate = useNavigate();
  const [activeService, setActiveService] = useState<ServiceMode>('car-me');
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [animatedVehicles, setAnimatedVehicles] = useState<AnimatedVehicle[]>([]);
  const [searchFormData, setSearchFormData] = useState<SearchFormData>({
    'hitch-freight': {
      destination: '',
      cargoType: '',
      pickupLocation: '',
      datetime: ''
    },
    'people-mover': {
      origin: '',
      destination: '',
      datetime: '',
      helpers: false
    },
    'car-me': {
      location: '',
      pickupDate: '',
      dropoffDate: '',
      carType: ''
    }
  });

  // Get user's current location on component mount
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserLocation(location);
        },
        (error) => {
          console.warn('Geolocation error:', error);
          // Default to New York City if geolocation fails
          setUserLocation({ lat: 40.7128, lng: -74.0060 });
        }
      );
    } else {
      // Default to New York City if geolocation is not supported
      setUserLocation({ lat: 40.7128, lng: -74.0060 });
    }
  }, []);

  // Generate animated vehicles when user location is available
  useEffect(() => {
    if (userLocation) {
      const vehicles = generateAnimatedVehicles(userLocation, activeService);
      setAnimatedVehicles(vehicles);
    }
  }, [userLocation, activeService]);

  const handleServiceChange = (service: ServiceMode) => {
    setActiveService(service);
    if (userLocation) {
      const vehicles = generateAnimatedVehicles(userLocation, service);
      setAnimatedVehicles(vehicles);
    }
  };

  const handleSearch = (formData: ServiceFormData<ServiceMode>) => {
    setSearchFormData(prev => ({
      ...prev,
      [activeService]: formData
    }));
    setSearchPerformed(true);

    // Navigate to search results with service mode and form data
    const searchParams = new URLSearchParams({
      service: activeService,
      ...formData as any
    });
    navigate(`/search?${searchParams.toString()}`);
  };

  const handleFilterChange = (filters: any) => {
    // Update filtered vehicles based on filter criteria
    if (userLocation) {
      const vehicles = generateAnimatedVehicles(userLocation, activeService, filters);
      setAnimatedVehicles(vehicles);
    }
  };

  return (
    <div className="min-h-screen bg-light-bg">
      {/* Header - transforms into FilterBar after search */}
      {searchPerformed ? (
        <FilterBar 
          activeService={activeService}
          onServiceChange={handleServiceChange}
          onFilterChange={handleFilterChange}
        />
      ) : (
        <GeometricHeader />
      )}

      {/* Main Split-Screen Layout */}
      <div className="flex flex-col lg:flex-row min-h-screen pt-16">
        {/* Left Side - Search Container */}
        <div className="w-full lg:w-1/2 relative">
          <div className="h-full bg-liquid-glass-olive">
            <AnimatedSearchContainer
              activeService={activeService}
              onServiceChange={handleServiceChange}
              onSearch={handleSearch}
              searchPerformed={searchPerformed}
              formData={searchFormData[activeService]}
            />
          </div>
        </div>

        {/* Right Side - Interactive Map */}
        <div className="w-full lg:w-1/2 relative">
          <div className="h-full min-h-[600px] lg:min-h-screen">
            <InteractiveMap
              userLocation={userLocation}
              animatedVehicles={animatedVehicles}
              activeService={activeService}
              onVehicleSelect={(vehicle) => {
                console.log('Selected vehicle:', vehicle);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandPage;
