import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Slider,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormControl,
  Box,
  Divider,
  IconButton,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import type {FilterOptions, VehicleFilterState, VehicleTypes} from '../types/models';

interface FilterModalProps {
  open: boolean;
  onClose: () => void;
  filters: FilterOptions;
  onApplyFilters: (filters: FilterOptions) => void;
}

const FilterModal = ({ open, onClose, filters, onApplyFilters }: FilterModalProps) => {
  const theme = useTheme();
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

  // Convert VehicleTypes[] to VehicleFilterState for UI
  const vehicleTypesToState = (types: VehicleTypes[]): VehicleFilterState => ({
    smallVan: types.includes('smallVan'),
    largeVan: types.includes('largeVan'),
    smallTruck: types.includes('smallTruck'),
    largeTruck: types.includes('largeTruck'),
    pickupTruck: types.includes('pickupTruck'),
    boxTruck: types.includes('boxTruck'),
  });

  // Convert VehicleFilterState to VehicleTypes[]
  const stateToVehicleTypes = (state: VehicleFilterState): VehicleTypes[] => {
    const types: VehicleTypes[] = [];
    if (state.smallVan) types.push('smallVan');
    if (state.largeVan) types.push('largeVan');
    if (state.smallTruck) types.push('smallTruck');
    if (state.largeTruck) types.push('largeTruck');
    if (state.pickupTruck) types.push('pickupTruck');
    if (state.boxTruck) types.push('boxTruck');
    return types;
  };

  const [vehicleFilterState, setVehicleFilterState] = useState<VehicleFilterState>(
    vehicleTypesToState(filters.vehicleTypes)
  );

  const handlePriceChange = (_event: Event, newValue: number | number[]) => {
    setLocalFilters({
      ...localFilters,
      priceRange: newValue as number[],
    });
  };

  const handleDistanceChange = (_event: Event, newValue: number | number[]) => {
    setLocalFilters({
      ...localFilters,
      distance: newValue as number,
    });
  };

  const handleVehicleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalFilters({
      ...localFilters,
      vehicleTypes: {
        ...localFilters.vehicleTypes,
        [event.target.name]: event.target.checked,
      },
    });
  };

  const handleSortChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalFilters({
      ...localFilters,
      sortBy: event.target.value,
    });
  };

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters: FilterOptions = {
      priceRange: [0, 150],
      distance: 10,
      vehicleTypes: {
        smallVan: false,
        largeVan: false,
        smallTruck: false,
        largeTruck: false,
        pickupTruck: false,
        boxTruck: false,
      },
      sortBy: 'recommended',
    };
    setLocalFilters(resetFilters);
    onApplyFilters(resetFilters);
  };

  // Count active filters
  const countActiveFilters = () => {
    let count = 0;

    // Check price range
    if (localFilters.priceRange[0] > 0 || localFilters.priceRange[1] < 150) {
      count++;
    }

    // Check distance
    if (localFilters.distance !== 10) {
      count++;
    }

    // Check vehicle types
    const activeVehicleTypes = Object.values(localFilters.vehicleTypes).filter(Boolean).length;
    if (activeVehicleTypes > 0) {
      count += activeVehicleTypes;
    }

    // Check sort
    if (localFilters.sortBy !== 'recommended') {
      count++;
    }

    return count;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        pb: 2
      }}>
        <Typography variant="h5" fontWeight={700}>Filters</Typography>
        <IconButton onClick={onClose} size="small" sx={{
          color: 'text.secondary',
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
            color: theme.palette.primary.main
          }
        }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ py: 3 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom fontWeight={600} sx={{ mb: 2 }}>
            Price Range (per hour)
          </Typography>
          <Slider
            value={localFilters.priceRange}
            onChange={handlePriceChange}
            valueLabelDisplay="auto"
            min={0}
            max={150}
            step={5}
            marks={[
              { value: 0, label: '$0' },
              { value: 150, label: '$150' },
            ]}
            sx={{
              color: theme.palette.primary.main,
              '& .MuiSlider-thumb': {
                height: 24,
                width: 24,
                backgroundColor: '#fff',
                border: `2px solid ${theme.palette.primary.main}`,
                '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
                  boxShadow: `0 0 0 8px ${alpha(theme.palette.primary.main, 0.16)}`,
                },
              },
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            ${localFilters.priceRange[0]} - ${localFilters.priceRange[1]}
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom fontWeight={600} sx={{ mb: 2 }}>
            Distance
          </Typography>
          <Slider
            value={localFilters.distance}
            onChange={handleDistanceChange}
            valueLabelDisplay="auto"
            min={1}
            max={50}
            marks={[
              { value: 1, label: '1 mi' },
              { value: 50, label: '50 mi' },
            ]}
            sx={{
              color: theme.palette.primary.main,
              '& .MuiSlider-thumb': {
                height: 24,
                width: 24,
                backgroundColor: '#fff',
                border: `2px solid ${theme.palette.primary.main}`,
                '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
                  boxShadow: `0 0 0 8px ${alpha(theme.palette.primary.main, 0.16)}`,
                },
              },
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Within {localFilters.distance} miles
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom fontWeight={600} sx={{ mb: 2 }}>
            Vehicle Type
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            {Object.entries(localFilters.vehicleTypes).map(([key, value]) => {
              const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
              return (
                <Chip
                  key={key}
                  label={label}
                  clickable
                  color={value ? 'primary' : 'default'}
                  variant={value ? 'filled' : 'outlined'}
                  onClick={() => {
                    setLocalFilters({
                      ...localFilters,
                      vehicleTypes: {
                        ...localFilters.vehicleTypes,
                        [key]: !value,
                      },
                    });
                  }}
                  sx={{
                    fontWeight: 500,
                    px: 1,
                    '&.MuiChip-filled': {
                      background: 'linear-gradient(90deg, #6366F1 0%, #8B5CF6 100%)',
                    }
                  }}
                />
              );
            })}
          </Box>
          <FormGroup sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 1 }}>
            {Object.entries(localFilters.vehicleTypes).map(([key, value]) => {
              const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
              return (
                <FormControlLabel
                  key={key}
                  control={
                    <Checkbox
                      checked={value}
                      onChange={handleVehicleTypeChange}
                      name={key}
                      sx={{
                        color: theme.palette.primary.main,
                        '&.Mui-checked': {
                          color: theme.palette.primary.main,
                        },
                      }}
                    />
                  }
                  label={label}
                />
              );
            })}
          </FormGroup>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom fontWeight={600} sx={{ mb: 2 }}>
            Sort By
          </Typography>
          <FormControl component="fieldset">
            <RadioGroup value={localFilters.sortBy} onChange={handleSortChange}>
              <FormControlLabel
                value="recommended"
                control={<Radio sx={{ color: theme.palette.primary.main, '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label="Recommended"
              />
              <FormControlLabel
                value="price_low"
                control={<Radio sx={{ color: theme.palette.primary.main, '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label="Price: Low to High"
              />
              <FormControlLabel
                value="price_high"
                control={<Radio sx={{ color: theme.palette.primary.main, '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label="Price: High to Low"
              />
              <FormControlLabel
                value="rating"
                control={<Radio sx={{ color: theme.palette.primary.main, '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label="Highest Rating"
              />
              <FormControlLabel
                value="distance"
                control={<Radio sx={{ color: theme.palette.primary.main, '&.Mui-checked': { color: theme.palette.primary.main } }} />}
                label="Closest First"
              />
            </RadioGroup>
          </FormControl>
        </Box>
      </DialogContent>

      <DialogActions sx={{
        px: 3,
        py: 2,
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Button
          onClick={handleReset}
          sx={{
            color: theme.palette.text.secondary,
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
            }
          }}
        >
          Reset All
        </Button>
        <Box>
          <Button onClick={onClose} sx={{ mr: 1 }}>
            Cancel
          </Button>
          <Button
            onClick={handleApply}
            variant="contained"
            color="primary"
            startIcon={countActiveFilters() > 0 ? <Chip
              label={countActiveFilters()}
              size="small"
              sx={{
                height: 20,
                minWidth: 20,
                backgroundColor: 'white',
                color: theme.palette.primary.main,
                fontWeight: 'bold'
              }}
            /> : undefined}
          >
            Apply Filters
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default FilterModal;
