import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { Filter, Package, Users, Car, ChevronDown } from 'lucide-react';
import type { ServiceMode } from '../types/models';

interface FilterBarProps {
  activeService: ServiceMode;
  onServiceChange: (service: ServiceMode) => void;
  onFilterChange: (filters: any) => void;
}

const FilterBar = ({ activeService, onServiceChange, onFilterChange }: FilterBarProps) => {
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: [0, 200],
    distance: 50,
    rating: 0,
    vehicleTypes: [] as string[],
    driverIncluded: false,
    sortBy: 'price'
  });

  const services = [
    {
      id: 'hitch-freight' as ServiceMode,
      name: 'Hitch Freight',
      icon: Package,
      color: 'olive-primary'
    },
    {
      id: 'people-mover' as ServiceMode,
      name: 'People Mover',
      icon: Users,
      color: 'gold-accent'
    },
    {
      id: 'car-me' as ServiceMode,
      name: 'Car Me',
      icon: Car,
      color: 'amber-accent'
    }
  ];

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const vehicleTypesByService = {
    'hitch-freight': ['van', 'truck', 'pickup'],
    'people-mover': ['van', 'truck', 'largeTruck'],
    'car-me': ['economy', 'compact', 'midsize', 'luxury', 'suv']
  };

  const handleVehicleTypeToggle = (type: string) => {
    const newTypes = filters.vehicleTypes.includes(type)
      ? filters.vehicleTypes.filter(t => t !== type)
      : [...filters.vehicleTypes, type];
    handleFilterChange('vehicleTypes', newTypes);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-liquid-glass-olive border-b-4 border-olive-primary backdrop-blur-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Filter Bar */}
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <RouterLink to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-olive-primary border-2 border-gold-accent flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                </svg>
              </div>
              <span className="text-xl font-black text-olive-dark">Movement</span>
            </RouterLink>
          </div>

          {/* Service Mode Selector */}
          <div className="flex items-center space-x-2">
            {services.map((service) => {
              const Icon = service.icon;
              return (
                <button
                  key={service.id}
                  onClick={() => onServiceChange(service.id)}
                  className={`
                    flex items-center space-x-2 px-4 py-2 border-2 transition-all duration-200 font-bold text-sm
                    ${activeService === service.id
                      ? 'border-gold-accent bg-liquid-glass-gold text-olive-dark'
                      : 'border-olive-primary text-olive-dark hover:border-gold-accent hover:bg-liquid-glass-subtle'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  <span>{service.name}</span>
                </button>
              );
            })}
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 border-2 border-olive-primary text-olive-dark hover:border-gold-accent hover:bg-liquid-glass-subtle transition-all duration-200 font-bold"
            >
              <Filter className="w-4 h-4" />
              <span>FILTERS</span>
              <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>

        {/* Expanded Filters Panel */}
        {showFilters && (
          <div className="border-t-2 border-olive-primary/30 py-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Price Range */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  PRICE RANGE ($/hour)
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="200"
                    value={filters.priceRange[1]}
                    onChange={(e) => handleFilterChange('priceRange', [filters.priceRange[0], parseInt(e.target.value)])}
                    className="w-full h-2 bg-olive-primary appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs font-medium text-olive-dark/70">
                    <span>${filters.priceRange[0]}</span>
                    <span>${filters.priceRange[1]}</span>
                  </div>
                </div>
              </div>

              {/* Distance */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  MAX DISTANCE (miles)
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="1"
                    max="100"
                    value={filters.distance}
                    onChange={(e) => handleFilterChange('distance', parseInt(e.target.value))}
                    className="w-full h-2 bg-olive-primary appearance-none cursor-pointer"
                  />
                  <div className="text-center text-xs font-medium text-olive-dark/70">
                    {filters.distance} miles
                  </div>
                </div>
              </div>

              {/* Vehicle Types */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  VEHICLE TYPES
                </label>
                <div className="space-y-2">
                  {vehicleTypesByService[activeService].map((type) => (
                    <label key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={filters.vehicleTypes.includes(type)}
                        onChange={() => handleVehicleTypeToggle(type)}
                        className="w-4 h-4 border-2 border-olive-primary focus:border-gold-accent"
                      />
                      <span className="text-sm font-medium text-olive-dark capitalize">
                        {type.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Rating */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  MIN RATING
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="5"
                    step="0.5"
                    value={filters.rating}
                    onChange={(e) => handleFilterChange('rating', parseFloat(e.target.value))}
                    className="w-full h-2 bg-olive-primary appearance-none cursor-pointer"
                  />
                  <div className="text-center text-xs font-medium text-olive-dark/70">
                    {filters.rating}+ stars
                  </div>
                </div>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  SORT BY
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full px-3 py-2 border-2 border-olive-primary focus:border-gold-accent focus:outline-none font-medium text-olive-dark liquid-glass-subtle"
                >
                  <option value="price">Price: Low to High</option>
                  <option value="price-desc">Price: High to Low</option>
                  <option value="distance">Distance: Nearest</option>
                  <option value="rating">Rating: Highest</option>
                  <option value="newest">Newest First</option>
                </select>
              </div>

              {/* Driver Included */}
              <div>
                <label className="block text-sm font-bold text-olive-dark mb-3">
                  OPTIONS
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={filters.driverIncluded}
                    onChange={(e) => handleFilterChange('driverIncluded', e.target.checked)}
                    className="w-4 h-4 border-2 border-olive-primary focus:border-gold-accent"
                  />
                  <span className="text-sm font-medium text-olive-dark">
                    Driver Included
                  </span>
                </label>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => {
                  const defaultFilters = {
                    priceRange: [0, 200],
                    distance: 50,
                    rating: 0,
                    vehicleTypes: [],
                    driverIncluded: false,
                    sortBy: 'price'
                  };
                  setFilters(defaultFilters);
                  onFilterChange(defaultFilters);
                }}
                className="px-4 py-2 border-2 border-olive-primary text-olive-dark hover:border-gold-accent hover:bg-liquid-glass-subtle transition-all duration-200 font-bold text-sm"
              >
                CLEAR FILTERS
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default FilterBar;
