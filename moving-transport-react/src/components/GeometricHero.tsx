import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const GeometricHero = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?location=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-liquid-silver-subtle overflow-hidden">
      {/* Geometric Background Elements */}
      <div className="absolute inset-0">
        {/* Large geometric shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 border-4 border-silver bg-silver-dark opacity-20"></div>
        <div className="absolute top-40 right-20 w-24 h-24 border-4 border-silver-darker bg-transparent"></div>
        <div className="absolute bottom-32 left-1/4 w-16 h-16 border-4 border-silver bg-silver-darker opacity-30"></div>
        <div className="absolute bottom-20 right-1/3 w-20 h-20 border-4 border-silver-dark bg-transparent"></div>
        
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 opacity-5">
          <div className="grid grid-cols-12 gap-4 h-full">
            {Array.from({ length: 144 }).map((_, i) => (
              <div key={i} className="border border-dark-text"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Heading */}
        <h1 className="text-4xl sm:text-5xl lg:text-7xl font-black text-dark-text leading-none mb-8">
          <span className="block">MOVEMENT</span>
          <span className="block text-silver-dark">TRANSPORT</span>
          <span className="block text-silver-darker">PLATFORM</span>
        </h1>

        {/* Subheading */}
        <p className="text-lg sm:text-xl lg:text-2xl font-bold text-secondary-text mb-12 max-w-2xl mx-auto leading-tight">
          CONNECT WITH AVAILABLE TRANSPORTATION SERVICES IN YOUR VICINITY. 
          GEOMETRIC. EFFICIENT. RELIABLE.
        </p>

        {/* Search Form */}
        <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-12">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="ENTER YOUR LOCATION..."
                className="w-full px-6 py-4 text-lg font-bold border-4 border-silver focus:border-silver-darker focus:outline-none text-dark-text placeholder-secondary-text bg-white"
                required
              />
            </div>
            <button
              type="submit"
              className="px-8 py-4 bg-olive border-4 border-lemongrass text-white font-black text-lg hover:bg-burnt-orange hover:border-burnt-orange transition-all duration-200 shadow-geometric hover:shadow-geometric-hover"
            >
              FIND TRANSPORT
            </button>
          </div>
        </form>

        {/* Key Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-olive border-4 border-lemongrass mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-black text-dark-text mb-2">LOCATION-BASED</h3>
            <p className="text-sm font-medium text-secondary-text">FIND TRANSPORT OPTIONS NEAR YOU</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-burnt-orange border-4 border-lemongrass mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
              </svg>
            </div>
            <h3 className="text-lg font-black text-dark-text mb-2">MULTI-MODAL</h3>
            <p className="text-sm font-medium text-secondary-text">TRUCKS AND VANS OF ALL SIZES</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-olive border-4 border-lemongrass mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-black text-dark-text mb-2">VERIFIED</h3>
            <p className="text-sm font-medium text-secondary-text">TRUSTED DRIVERS AND VEHICLES</p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-16 pt-12 border-t-4 border-lemongrass">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-black text-olive mb-2">500+</div>
              <div className="text-sm font-bold text-secondary-text">VEHICLES</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-black text-burnt-orange mb-2">50+</div>
              <div className="text-sm font-bold text-secondary-text">CITIES</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-black text-olive mb-2">10K+</div>
              <div className="text-sm font-bold text-secondary-text">BOOKINGS</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-black text-burnt-orange mb-2">4.9</div>
              <div className="text-sm font-bold text-secondary-text">RATING</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GeometricHero;
