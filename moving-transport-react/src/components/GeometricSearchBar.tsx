import { useState } from 'react';
import type { FilterOptions, VehicleTypes } from '../types';

interface GeometricSearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearch: () => void;
  filters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
}

const GeometricSearchBar = ({
  searchQuery,
  onSearchChange,
  onSearch,
  filters,
  onFilterChange,
}: GeometricSearchBarProps) => {
  const [showFilters, setShowFilters] = useState(false);

  const vehicleTypes: VehicleTypes[] = ['van', 'truck', 'pickup'];

  const handleVehicleTypeToggle = (type: VehicleTypes) => {
    const newTypes = filters.vehicleTypes.includes(type)
      ? filters.vehicleTypes.filter(t => t !== type)
      : [...filters.vehicleTypes, type];
    
    onFilterChange({ ...filters, vehicleTypes: newTypes });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch();
  };

  return (
    <div className="liquid-glass-container border-4 border-olive-primary/30 p-6">
      {/* Main Search Form */}
      <form onSubmit={handleSubmit} className="mb-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="location" className="block text-sm font-bold text-olive-dark mb-2">
              LOCATION
            </label>
            <input
              id="location"
              type="text"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Enter your location..."
              className="w-full px-4 py-3 border-2 border-olive-primary focus:border-gold-accent focus:outline-none font-medium text-dark-text placeholder-secondary-text liquid-glass-subtle"
            />
          </div>
          <div className="flex items-end">
            <button
              type="submit"
              className="btn-geometric h-12 px-8 liquid-glass-gold border-2 border-gold-accent hover:border-amber-accent"
            >
              SEARCH
            </button>
          </div>
        </div>
      </form>

      {/* Filter Toggle */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 text-secondary-text hover:text-dark-text font-medium"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="square" strokeLinejoin="miter" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
          </svg>
          <span>FILTERS</span>
          <svg className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="square" strokeLinejoin="miter" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        
        <div className="text-sm text-secondary-text font-medium">
          {filters.vehicleTypes.length > 0 && `${filters.vehicleTypes.length} vehicle type(s) selected`}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="mt-6 pt-6 border-t-2 border-lemongrass">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Price Range */}
            <div>
              <label className="block text-sm font-bold text-dark-text mb-3">
                PRICE RANGE ($/hour)
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="200"
                  value={filters.priceRange[1]}
                  onChange={(e) => onFilterChange({
                    ...filters,
                    priceRange: [filters.priceRange[0], parseInt(e.target.value)]
                  })}
                  className="w-full h-2 bg-lemongrass appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs font-medium text-secondary-text">
                  <span>${filters.priceRange[0]}</span>
                  <span>${filters.priceRange[1]}</span>
                </div>
              </div>
            </div>

            {/* Distance */}
            <div>
              <label className="block text-sm font-bold text-dark-text mb-3">
                MAX DISTANCE (miles)
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="1"
                  max="100"
                  value={filters.distance}
                  onChange={(e) => onFilterChange({
                    ...filters,
                    distance: parseInt(e.target.value)
                  })}
                  className="w-full h-2 bg-lemongrass appearance-none cursor-pointer"
                />
                <div className="text-center text-xs font-medium text-secondary-text">
                  {filters.distance} miles
                </div>
              </div>
            </div>

            {/* Vehicle Types */}
            <div>
              <label className="block text-sm font-bold text-dark-text mb-3">
                VEHICLE TYPE
              </label>
              <div className="space-y-2">
                {vehicleTypes.map((type) => (
                  <label key={type} className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.vehicleTypes.includes(type)}
                      onChange={() => handleVehicleTypeToggle(type)}
                      className="w-4 h-4 border-2 border-lemongrass text-olive focus:ring-0 focus:ring-offset-0"
                    />
                    <span className="ml-2 text-sm font-medium text-dark-text capitalize">
                      {type}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Rating */}
            <div>
              <label className="block text-sm font-bold text-dark-text mb-3">
                MIN RATING
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="5"
                  step="0.5"
                  value={filters.rating}
                  onChange={(e) => onFilterChange({
                    ...filters,
                    rating: parseFloat(e.target.value)
                  })}
                  className="w-full h-2 bg-lemongrass appearance-none cursor-pointer"
                />
                <div className="text-center text-xs font-medium text-secondary-text">
                  {filters.rating} stars
                </div>
              </div>
            </div>
          </div>

          {/* Driver Included Toggle */}
          <div className="mt-6 pt-4 border-t-2 border-lemongrass">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filters.driverIncluded}
                onChange={(e) => onFilterChange({
                  ...filters,
                  driverIncluded: e.target.checked
                })}
                className="w-4 h-4 border-2 border-lemongrass text-olive focus:ring-0 focus:ring-offset-0"
              />
              <span className="ml-2 text-sm font-bold text-dark-text">
                DRIVER INCLUDED ONLY
              </span>
            </label>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeometricSearchBar;
