import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import GeometricHeader from './GeometricHeader';
import GeometricTransportCard from './GeometricTransportCard';
import GeometricSearchBar from './GeometricSearchBar';
import MapComponent from './MapComponent';
import { transportOptions } from '../data/mockData';
import type { TransportOption, FilterOptions, VehicleTypes } from '../types';

const GeometricSearchResults = () => {
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredResults, setFilteredResults] = useState<TransportOption[]>([]);
  const [selectedTransport, setSelectedTransport] = useState<TransportOption | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    priceRange: [0, 200],
    distance: 50,
    vehicleTypes: [] as VehicleTypes[],
    rating: 0,
    driverIncluded: false,
    sortBy: 'price',
  });

  // Parse query parameters and scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);

    const params = new URLSearchParams(location.search);
    const locationParam = params.get('location');

    if (locationParam) {
      setSearchQuery(locationParam);
    }

    // Simulate loading data
    setTimeout(() => {
      setLoading(false);
      setFilteredResults(transportOptions);
    }, 1000);
  }, [location.search]);

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleSearchSubmit = () => {
    // Implement search logic here
    console.log('Searching for:', searchQuery);
  };

  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
    // Apply filters to results
    const filtered = transportOptions.filter(transport => {
      const price = parseInt(transport.price.replace('$', ''));
      return (
        price >= newFilters.priceRange[0] &&
        price <= newFilters.priceRange[1] &&
        transport.distance <= newFilters.distance &&
        transport.rating >= newFilters.rating &&
        (newFilters.vehicleTypes.length === 0 || newFilters.vehicleTypes.includes(transport.type as VehicleTypes)) &&
        (!newFilters.driverIncluded || transport.driverIncluded)
      );
    });
    setFilteredResults(filtered);
  };

  const handleTransportSelect = (transport: TransportOption) => {
    setSelectedTransport(transport);
    // Scroll to map on mobile
    if (window.innerWidth < 768) {
      document.getElementById('map-section')?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleMarkerClick = (transport: TransportOption) => {
    setSelectedTransport(transport);
    // Scroll to the specific transport card
    document.getElementById(`transport-${transport.id}`)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen liquid-glass-container">
      <GeometricHeader />

      {/* Main Content */}
      <div className="pt-20 pb-8">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              <li>
                <RouterLink to="/" className="text-secondary-text hover:text-dark-text font-medium">
                  Home
                </RouterLink>
              </li>
              <li>
                <svg className="w-4 h-4 text-secondary-text" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </li>
              <li>
                <span className="text-dark-text font-bold">Search Results</span>
              </li>
            </ol>
          </nav>

          {/* Search Bar */}
          <div className="mb-8">
            <div className="liquid-glass-olive border-2 border-olive-primary/30">
              <GeometricSearchBar
                searchQuery={searchQuery}
                onSearchChange={handleSearchChange}
                onSearch={handleSearchSubmit}
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </div>
          </div>

          {/* Results Summary */}
          <div className="mb-6 liquid-glass-subtle p-6 border-2 border-gold-accent/20">
            <h1 className="text-2xl font-black text-dark-text mb-2">
              Transport Options {searchQuery && `in ${searchQuery}`}
            </h1>
            <p className="text-secondary-text font-medium">
              {loading ? 'Loading...' : `${filteredResults.length} vehicles available`}
            </p>
            <div className="mt-3 flex items-center space-x-2">
              <div className="w-3 h-3 bg-olive-primary border border-gold-accent"></div>
              <div className="w-3 h-3 bg-gold-accent border border-olive-primary"></div>
              <div className="w-3 h-3 bg-amber-accent border border-olive-dark"></div>
              <span className="text-xs font-bold text-olive-dark ml-2">LIQUID GLASS DESIGN</span>
            </div>
          </div>

          {/* Loading State */}
          {loading ? (
            <div className="flex justify-center items-center py-16 liquid-glass-olive">
              <div className="w-8 h-8 border-4 border-olive-primary border-t-gold-accent animate-spin"></div>
            </div>
          ) : (
            /* Main Layout - 60/40 Split - Full Width */
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 min-h-[600px] w-full">
              {/* Product Listings - 60% (3/5 columns) */}
              <div className="lg:col-span-3 space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto pr-4 liquid-glass-subtle border-2 border-olive-primary/20 p-4">
                {filteredResults.length > 0 ? (
                  filteredResults.map((transport) => (
                    <div key={transport.id} id={`transport-${transport.id}`}>
                      <GeometricTransportCard
                        transport={transport}
                        selected={selectedTransport?.id === transport.id}
                        onClick={() => handleTransportSelect(transport)}
                      />
                    </div>
                  ))
                ) : (
                  <div className="text-center py-16 liquid-glass-gold">
                    <div className="w-16 h-16 bg-olive-primary border-2 border-gold-accent mx-auto mb-4 flex items-center justify-center liquid-glass-olive">
                      <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-black text-dark-text mb-2">No Results Found</h3>
                    <p className="text-secondary-text">Try adjusting your search criteria or filters.</p>
                  </div>
                )}
              </div>

              {/* Map Section - 40% (2/5 columns) */}
              <div className="lg:col-span-2" id="map-section">
                <div className="sticky top-24 h-[calc(100vh-200px)] min-h-[500px] border-4 border-olive-primary liquid-glass-container">
                  <div className="liquid-glass-subtle h-full">
                    <MapComponent
                      transportOptions={filteredResults}
                      selectedTransport={selectedTransport}
                      onMarkerClick={handleMarkerClick}
                    />
                  </div>
                  {/* Liquid glass overlay for map */}
                  <div className="absolute top-4 right-4 liquid-glass-gold p-2 border border-gold-accent/30">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-olive-primary"></div>
                      <div className="w-2 h-2 bg-gold-accent"></div>
                      <span className="text-xs font-bold text-olive-dark">LIVE MAP</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GeometricSearchResults;
