/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Apple iOS 25 Liquid Glass - Olive Green Primary with Gold Accents
        'olive-primary': '#6B7C32',
        'olive-light': '#8A9B52',
        'olive-dark': '#4A5622',
        'olive-darker': '#3A4419',
        'gold-accent': '#D4AF37',
        'gold-light': '#E6C547',
        'gold-dark': '#B8941F',
        'amber-accent': '#FFC107',
        'amber-light': '#FFD54F',
        'amber-dark': '#FF8F00',
        // Liquid Silver Monochromatic color palette (maintained for compatibility)
        'silver-light': '#E8E8E8',
        'silver': '#C0C0C0',
        'silver-dark': '#A8A8A8',
        'silver-darker': '#808080',
        'silver-darkest': '#606060',
        'light-bg': '#F5F5F5',
        'dark-text': '#2C2C2C',
        'secondary-text': '#4A4A4A',
        // Legacy color aliases for existing components
        'lemongrass': '#6B7C32', // Maps to olive-primary
        'burnt-orange': '#D4AF37', // Maps to gold-accent
        'olive': '#4A5622', // Maps to olive-dark
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        // Ensure all spacing follows 4px grid system
        '18': '4.5rem',
        '22': '5.5rem',
        '26': '6.5rem',
        '30': '7.5rem',
      },
      borderRadius: {
        // Override all border radius to be sharp (none)
        'none': '0',
        'DEFAULT': '0',
      },
      boxShadow: {
        'geometric': '4px 4px 0px 0px rgba(0, 0, 0, 0.1)',
        'geometric-hover': '8px 8px 0px 0px rgba(0, 0, 0, 0.15)',
        'geometric-lg': '8px 8px 0px 0px rgba(0, 0, 0, 0.1)',
      },
      backgroundImage: {
        // Apple iOS 25 Liquid Glass Gradients
        'liquid-glass-olive': 'linear-gradient(135deg, rgba(107, 124, 50, 0.1) 0%, rgba(138, 155, 82, 0.15) 25%, rgba(74, 86, 34, 0.2) 50%, rgba(138, 155, 82, 0.15) 75%, rgba(107, 124, 50, 0.1) 100%)',
        'liquid-glass-gold': 'linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(230, 197, 71, 0.15) 50%, rgba(184, 148, 31, 0.1) 100%)',
        'liquid-glass-primary': 'linear-gradient(135deg, rgba(107, 124, 50, 0.05) 0%, rgba(212, 175, 55, 0.08) 25%, rgba(74, 86, 34, 0.12) 50%, rgba(212, 175, 55, 0.08) 75%, rgba(107, 124, 50, 0.05) 100%)',
        'liquid-glass-overlay': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%)',
        // Legacy gradients (maintained for compatibility)
        'liquid-silver': 'linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 25%, #C0C0C0 50%, #E8E8E8 75%, #F5F5F5 100%)',
        'liquid-silver-subtle': 'linear-gradient(135deg, #F8F8F8 0%, #F0F0F0 50%, #F8F8F8 100%)',
      },
      backdropFilter: {
        'none': 'none',
        'blur': 'blur(20px)',
        'blur-sm': 'blur(4px)',
        'blur-md': 'blur(12px)',
        'blur-lg': 'blur(16px)',
        'blur-xl': 'blur(24px)',
        'blur-2xl': 'blur(40px)',
        'blur-3xl': 'blur(64px)',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.liquid-glass': {
          'backdrop-filter': 'blur(20px) saturate(180%)',
          '-webkit-backdrop-filter': 'blur(20px) saturate(180%)',
          'background': 'rgba(255, 255, 255, 0.1)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.liquid-glass-olive': {
          'backdrop-filter': 'blur(20px) saturate(180%)',
          '-webkit-backdrop-filter': 'blur(20px) saturate(180%)',
          'background': 'rgba(107, 124, 50, 0.1)',
          'border': '1px solid rgba(107, 124, 50, 0.2)',
        },
        '.liquid-glass-gold': {
          'backdrop-filter': 'blur(20px) saturate(180%)',
          '-webkit-backdrop-filter': 'blur(20px) saturate(180%)',
          'background': 'rgba(212, 175, 55, 0.1)',
          'border': '1px solid rgba(212, 175, 55, 0.2)',
        },
        '.liquid-glass-subtle': {
          'backdrop-filter': 'blur(12px) saturate(150%)',
          '-webkit-backdrop-filter': 'blur(12px) saturate(150%)',
          'background': 'rgba(255, 255, 255, 0.05)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}
